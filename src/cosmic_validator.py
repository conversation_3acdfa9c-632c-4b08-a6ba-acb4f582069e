#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验器

对main.py生成的cosmic功能拆解excel文件进行校验，分为两部分：

A. Python程序检查（输出检查结果）：
1. 不同行的数据属性必须不能相同
2. 不同行的功能过程、子过程不能完全相同
3. 每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E

B. LLM检查并修复：
4. 数据属性必须用中文，可以有中文+英文缩写，如：操作日志ID
5. 数据组的内容需要包含三级模块中的主要实体名称

C. 修复后的结果更新到原来的数据集中，并输出到新文件中
"""

import pandas as pd
import json
import os
import time
import threading
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, OrderedDict, Counter
from typing import Dict, List, Any, Tuple, Set
from csv_2_xls import process_csv_to_excel
import sys
# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import llm_util
from config import ( CHECK_EXCLUDED_FIELDS,
    CHECK_OUTPUT_DIR, CHECK_RESULT_FILE,
    CHECK_PROMPT_FILE, CHECK_BATCH_COUNT, THREAD_COUNT, MAX_THREAD_COUNT
)


class CosmicValidator:
    """COSMIC校验器类 - 分离式校验"""

    def __init__(self):
        """初始化校验器"""
        self.llm_prompt = self._load_llm_prompt()
        self.excluded_fields = CHECK_EXCLUDED_FIELDS or []
        self.batch_count = CHECK_BATCH_COUNT or 50
        self.output_dir = CHECK_OUTPUT_DIR or "debug"

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        print(f"COSMIC校验器初始化完成")
        print(f"- 批次大小: {self.batch_count}")
        print(f"- 输出目录: {self.output_dir}")
        print(f"- 排除字段: {self.excluded_fields}")

    def _load_llm_prompt(self) -> str:
        """加载LLM校验提示词"""
        try:
            with open(CHECK_PROMPT_FILE, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"警告: 找不到提示词文件 {CHECK_PROMPT_FILE}，使用默认提示词")
            return self._get_default_llm_prompt()
        except Exception as e:
            print(f"加载提示词失败: {e}，使用默认提示词")
            return self._get_default_llm_prompt()

    def _get_default_llm_prompt(self) -> str:
        """获取默认LLM提示词"""
        return """你是COSMIC评审专家，专门负责检查和修复以下问题：
1. 数据属性必须用中文，可以有中文+英文缩写，如：操作日志ID
2. 数据组的内容需要包含三级模块中的主要实体名称

请严格按照JSON格式输出修复结果。"""
    
    # ==================== Python程序检查方法 ====================

    def check_duplicate_data_attributes(self, df: pd.DataFrame) -> List[Dict]:
        """检查1: 不同行的数据属性必须不能相同"""
        issues = []
        data_attr_col = '数据属性'

        if data_attr_col not in df.columns:
            return issues

        # 统计数据属性出现次数
        attr_counts = df[data_attr_col].value_counts()
        duplicates = attr_counts[attr_counts > 1]

        for attr, count in duplicates.items():
            if pd.notna(attr) and str(attr).strip():
                duplicate_rows = df[df[data_attr_col] == attr].index.tolist()
                issues.append({
                    "issue_type": "数据属性重复",
                    "description": f"数据属性 '{attr}' 在 {count} 行中重复出现",
                    "affected_rows": duplicate_rows,
                    "severity": "高",
                    "value": attr
                })

        return issues

    def check_duplicate_function_processes(self, df: pd.DataFrame) -> List[Dict]:
        """检查2: 不同行的功能过程、子过程不能完全相同"""
        issues = []
        func_process_col = '功能过程'
        subprocess_col = '子过程描述'

        if func_process_col not in df.columns or subprocess_col not in df.columns:
            return issues

        # 创建功能过程+子过程的组合键
        df_temp = df.copy()
        df_temp['process_key'] = df_temp[func_process_col].astype(str) + "||" + df_temp[subprocess_col].astype(str)

        # 统计组合键出现次数
        process_counts = df_temp['process_key'].value_counts()
        duplicates = process_counts[process_counts > 1]

        for process_key, count in duplicates.items():
            if "||" in process_key:
                func_process, subprocess = process_key.split("||", 1)
                if func_process.strip() and subprocess.strip():
                    duplicate_rows = df_temp[df_temp['process_key'] == process_key].index.tolist()
                    issues.append({
                        "issue_type": "功能过程重复",
                        "description": f"功能过程 '{func_process}' + 子过程 '{subprocess}' 在 {count} 行中重复出现",
                        "affected_rows": duplicate_rows,
                        "severity": "高",
                        "function_process": func_process,
                        "subprocess": subprocess
                    })

        return issues

    def check_data_movement_sequence(self, df: pd.DataFrame) -> List[Dict]:
        """检查3: 每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E"""
        issues = []
        func_process_col = '功能过程'
        movement_col = '数据移动类型'

        if func_process_col not in df.columns or movement_col not in df.columns:
            return issues

        # 按功能过程分组
        grouped = df.groupby(func_process_col)

        for func_process, group in grouped:
            if pd.isna(func_process) or not str(func_process).strip():
                continue

            movements = group[movement_col].dropna().astype(str).str.strip().tolist()
            if not movements:
                continue

            group_rows = group.index.tolist()

            # 检查第一步是否为E
            if movements[0] != 'E':
                issues.append({
                    "issue_type": "数据移动序列错误",
                    "description": f"功能过程 '{func_process}' 的第一步不是E，而是 '{movements[0]}'",
                    "affected_rows": [group_rows[0]],
                    "severity": "高",
                    "function_process": func_process,
                    "expected": "E",
                    "actual": movements[0]
                })

            # 检查最后一步是否为W或X
            if len(movements) > 1 and movements[-1] not in ['W', 'X']:
                issues.append({
                    "issue_type": "数据移动序列错误",
                    "description": f"功能过程 '{func_process}' 的最后一步不是W或X，而是 '{movements[-1]}'",
                    "affected_rows": [group_rows[-1]],
                    "severity": "高",
                    "function_process": func_process,
                    "expected": "W或X",
                    "actual": movements[-1]
                })

            # 检查是否有连续的E
            for i in range(len(movements) - 1):
                if movements[i] == 'E' and movements[i + 1] == 'E':
                    issues.append({
                        "issue_type": "数据移动序列错误",
                        "description": f"功能过程 '{func_process}' 存在连续的E",
                        "affected_rows": [group_rows[i], group_rows[i + 1]],
                        "severity": "中",
                        "function_process": func_process,
                        "position": f"第{i+1}和第{i+2}步"
                    })

        return issues

    def run_python_checks(self, df: pd.DataFrame) -> Dict:
        """运行Python程序检查（只检查结构性问题）"""
        print("开始Python程序检查...")

        all_issues = []

        # 检查1: 数据属性重复
        print("  检查1: 数据属性重复...")
        attr_issues = self.check_duplicate_data_attributes(df)
        all_issues.extend(attr_issues)
        print(f"    发现 {len(attr_issues)} 个数据属性重复问题")

        # 检查2: 功能过程重复
        print("  检查2: 功能过程重复...")
        process_issues = self.check_duplicate_function_processes(df)
        all_issues.extend(process_issues)
        print(f"    发现 {len(process_issues)} 个功能过程重复问题")

        # 注意：数据移动序列检查已移至LLM处理

        # 统计问题类型
        issue_types = {}
        for issue in all_issues:
            issue_type = issue.get('issue_type', '未知')
            issue_types[issue_type] = issue_types.get(issue_type, 0) + 1

        result = {
            "total_issues": len(all_issues),
            "issue_types": issue_types,
            "issues": all_issues,
            "summary": f"Python程序检查完成，共发现 {len(all_issues)} 个问题"
        }

        print(f"Python程序检查完成，共发现 {len(all_issues)} 个问题")
        return result

    # ==================== 文件处理方法 ====================

    def parse_excel_file(self, file_path: str) -> pd.DataFrame:
        """解析Excel或CSV文件"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path)            
            
            # 向后填充一级/二级模块列
            level_1_name, level_2_name, level_3_name, user,event,process_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
            df[[level_1_name, level_2_name, level_3_name, user,event,process_name]] = df[[level_1_name, level_2_name, level_3_name, user,event,process_name]].ffill()
            
            print(f"成功解析文件: {file_path}")
            print(f"数据行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            
            return df
        except Exception as e:
            print(f"解析文件失败: {e}")
            return None
    
    def group_by_level2_module(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """按二级模块分组数据"""
        grouped_data = defaultdict(list)
        
        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row.get('三级功能模块')) or str(row.get('三级功能模块')).strip() == '':
                continue
            
            level2_module = str(row.get('二级功能模块', ''))
            
            # 转换为字典，排除指定字段
            row_dict = {}
            for col, value in row.items():
                if col not in self.excluded_fields:
                    row_dict[col] = value if not pd.isna(value) else ""
            
            grouped_data[level2_module].append(row_dict)
        
        print(f"按二级模块分组完成，共 {len(grouped_data)} 个模块")
        for module, data in grouped_data.items():
            print(f"  {module}: {len(data)} 条记录")
        
        return dict(grouped_data)

    # ==================== LLM检查和修复方法 ====================

    def _call_llm_for_validation(self, data_batch: List[Dict], batch_id: str) -> Dict:
        """调用大模型进行LLM校验和修复"""
        # 将数据按三级模块组织
        level3_grouped = self._group_by_level3_for_llm(data_batch)

        # 构建用户输入，包含需要LLM检查的三类问题
        user_input = f"请检查并修复以下COSMIC数据中的问题（批次{batch_id}）：\n\n"
        user_input += "需要检查的问题：\n"
        user_input += "1. 数据属性必须用中文，可以有中文+英文缩写，如：操作日志ID\n"
        user_input += "2. 数据组的内容需要包含三级模块中的主要实体名称\n"
        user_input += "3. 每个功能过程的数据移动类型序列：第一步必须是E，最后一步必须是W或X，不能有连续的E\n\n"
        user_input += "数据（按三级模块组织）：\n"
        user_input += json.dumps(level3_grouped, ensure_ascii=False, indent=2)

        try:
            # 调用LLM，使用默认配置
            result = llm_util.call_LLM(self.llm_prompt, user_input + "")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result:
                    return parsed_result
                else:
                    print(f"批次 {batch_id} JSON解析失败")
                    return {"error": "JSON解析失败", "raw_content": result}
            else:
                print(f"批次 {batch_id} LLM调用失败")
                return {"error": "LLM调用失败"}

        except Exception as e:
            print(f"批次 {batch_id} 处理异常: {e}")
            return {"error": str(e)}

    def _group_by_level3_for_llm(self, data_batch: List[Dict]) -> List[Dict]:
        """将数据按三级模块分组，用于LLM处理"""
        level3_groups = defaultdict(lambda: defaultdict(list))

        for record in data_batch:
            level3_module = record.get('三级功能模块', '')
            function_process = record.get('功能过程', '')

            # 构建子过程信息
            subprocess_info = {
                "子过程描述": record.get('子过程描述', ''),
                "数据移动类型": record.get('数据移动类型', ''),
                "数据组": record.get('数据组', ''),
                "数据属性": record.get('数据属性', ''),
                "CFP": record.get('CFP', 1),
                "修复说明": ""
            }

            level3_groups[level3_module][function_process].append(subprocess_info)

        # 转换为目标格式
        result = []
        for level3_module, processes in level3_groups.items():
            module_data = {}
            process_list = []

            for function_process, subprocesses in processes.items():
                # 获取第一个记录的其他信息
                first_record = next((r for r in data_batch
                                   if r.get('三级功能模块') == level3_module
                                   and r.get('功能过程') == function_process), {})

                process_info = {
                    "功能用户": first_record.get('功能用户', ''),
                    "触发事件": first_record.get('触发事件', ''),
                    "功能过程": function_process,
                    "子过程": subprocesses
                }
                process_list.append(process_info)

            module_data[level3_module] = process_list
            result.append(module_data)

        return result

    def _convert_llm_result_to_flat_format(self, llm_result: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """将LLM的三级模块结构结果转换为扁平格式"""
        fixed_data = []
        issues_found = []

        for module_item in llm_result:
            for level3_module, processes in module_item.items():
                for process in processes:
                    function_process = process.get('功能过程', '')
                    function_user = process.get('功能用户', '')
                    trigger_event = process.get('触发事件', '')

                    subprocesses = process.get('子过程', [])
                    for subprocess in subprocesses:
                        # 构建扁平记录
                        record = {
                            "一级功能模块": "",  # 需要从原始数据中获取
                            "二级功能模块": "",  # 需要从原始数据中获取
                            "三级功能模块": level3_module,
                            "功能用户": function_user,
                            "触发事件": trigger_event,
                            "功能过程": function_process,
                            "子过程描述": subprocess.get('子过程描述', ''),
                            "数据移动类型": subprocess.get('数据移动类型', ''),
                            "数据组": subprocess.get('数据组', ''),
                            "数据属性": subprocess.get('数据属性', ''),
                            "CFP": subprocess.get('CFP', 1)
                        }
                        fixed_data.append(record)

                        # 收集修复说明
                        fix_note = subprocess.get('修复说明', '')
                        if fix_note and fix_note.strip():
                            issue = {
                                "issue_type": "LLM修复",
                                "description": fix_note,
                                "fix_applied": fix_note,
                                "level3_module": level3_module,
                                "function_process": function_process
                            }
                            issues_found.append(issue)

        return fixed_data, issues_found

    def _supplement_module_info(self, fixed_data: List[Dict], original_batch: List[Dict]) -> List[Dict]:
        """补充一级和二级模块信息"""
        # 创建三级模块到一级、二级模块的映射
        module_mapping = {}
        for record in original_batch:
            level3 = record.get('三级功能模块', '')
            if level3:
                module_mapping[level3] = {
                    "一级功能模块": record.get('一级功能模块', ''),
                    "二级功能模块": record.get('二级功能模块', '')
                }

        # 补充信息
        for record in fixed_data:
            level3 = record.get('三级功能模块', '')
            if level3 in module_mapping:
                record["一级功能模块"] = module_mapping[level3]["一级功能模块"]
                record["二级功能模块"] = module_mapping[level3]["二级功能模块"]

        return fixed_data

    def run_llm_checks_and_fixes(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """运行LLM检查和修复"""
        print("开始LLM检查和修复...")

        # 按二级模块分组进行LLM处理
        grouped_data = self.group_by_level2_module(df)
        if not grouped_data:
            print("没有有效数据进行LLM处理")
            return df, []

        # 确定线程数
        thread_count = self.get_optimal_thread_count(len(grouped_data))
        print(f"使用 {thread_count} 个线程进行LLM处理")

        # 多线程LLM处理
        start_time = time.time()
        all_results = {}

        with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicLLM") as executor:
            # 提交任务
            future_to_module = {}
            for module_name, module_data in grouped_data.items():
                future = executor.submit(self.process_module_with_llm, module_name, module_data)
                future_to_module[future] = module_name

            # 收集结果
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    result_module_name, result = future.result()
                    all_results[result_module_name] = result
                except Exception as exc:
                    print(f"模块 {module_name} LLM处理失败: {exc}")
                    all_results[module_name] = {"error": str(exc), "original_data": grouped_data[module_name]}

        processing_time = time.time() - start_time
        print(f"LLM处理完成，耗时: {processing_time:.2f}秒")

        # 合并修复后的数据
        fixed_df, llm_issues = self._merge_llm_results(all_results, df)

        return fixed_df, llm_issues

    def process_module_with_llm(self, module_name: str, module_data: List[Dict]) -> Tuple[str, Dict]:
        """使用LLM处理单个二级模块"""
        print(f"[线程{threading.current_thread().name}] 开始LLM处理模块: {module_name}")

        # 分批处理
        batches = []
        for i in range(0, len(module_data), self.batch_count):
            batch = module_data[i:i + self.batch_count]
            batches.append(batch)

        print(f"[线程{threading.current_thread().name}] 模块 {module_name} 分为 {len(batches)} 个批次")

        # 处理每个批次
        all_fixed_data = []
        all_issues = []

        for batch_idx, batch in enumerate(batches):
            batch_id = f"{module_name}_llm_batch_{batch_idx + 1}"

            # 保存输入数据用于调试
            # debug_file = os.path.join(self.output_dir, f"{batch_id}_input.json")
            # try:
            #     with open(debug_file, 'w', encoding='utf-8') as f:
            #         json.dump(batch, f, ensure_ascii=False, indent=2)
            # except Exception as e:
            #     print(f"保存调试文件失败: {e}")

            # 调用LLM处理
            print(f"批处理输入：{batch}")
            result = self._call_llm_for_validation(batch, batch_id)

            if "error" in result:
                print(f"批次 {batch_id} LLM处理失败: {result['error']}")
                # 如果LLM处理失败，保留原始数据
                all_fixed_data.extend(batch)
            else:
                print(f"批次 {batch_id} 结果: {result}")
                # 处理新的三级模块结构的LLM结果
                if isinstance(result, list):
                    # 直接是三级模块列表
                    fixed_data, issues = self._convert_llm_result_to_flat_format(result)

                    # 补充一级和二级模块信息
                    fixed_data = self._supplement_module_info(fixed_data, batch)

                    all_fixed_data.extend(fixed_data)
                    all_issues.extend(issues)

                    print(f"批次 {batch_id} LLM处理完成: 处理 {len(fixed_data)} 条记录，发现 {len(issues)} 个问题")
                else:
                    # 兼容旧格式
                    fixed_data = result.get('fixed_data', batch)
                    additional_processes = result.get('additional_processes', [])
                    issues = result.get('issues_found', [])

                    # 合并修复的数据和新增的子过程
                    all_fixed_data.extend(fixed_data)
                    if additional_processes:
                        all_fixed_data.extend(additional_processes)
                        print(f"批次 {batch_id} 新增了 {len(additional_processes)} 个子过程")

                    all_issues.extend(issues)

                    total_records = len(fixed_data) + len(additional_processes)
                    print(f"批次 {batch_id} LLM处理完成: 处理 {total_records} 条记录，发现 {len(issues)} 个问题")

        # 汇总结果
        module_result = {
            "module_name": module_name,
            "original_count": len(module_data),
            "fixed_count": len(all_fixed_data),
            "fixed_data": all_fixed_data,
            "issues_found": all_issues
        }

        print(f"[线程{threading.current_thread().name}] 完成LLM处理模块 {module_name}: 原始{len(module_data)}条，修复{len(all_fixed_data)}条")

        return module_name, module_result

    def _merge_llm_results(self, all_results: Dict, original_df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """合并LLM处理结果"""
        all_fixed_data = []
        all_issues = []

        for module_name, result in all_results.items():
            if "error" in result:
                print(f"模块 {module_name} 有错误，使用原始数据: {result['error']}")
                # 使用原始数据
                if "original_data" in result:
                    all_fixed_data.extend(result["original_data"])
                continue

            all_fixed_data.extend(result.get('fixed_data', []))
            all_issues.extend(result.get('issues_found', []))

        # 如果没有修复数据，返回原始数据
        if not all_fixed_data:
            print("没有LLM修复数据，返回原始数据")
            return original_df, all_issues

        # 创建修复后的DataFrame
        try:
            fixed_df = pd.DataFrame(all_fixed_data)
            print(f"LLM修复完成: 原始 {len(original_df)} 条 -> 修复后 {len(fixed_df)} 条")
            return fixed_df, all_issues
        except Exception as e:
            print(f"创建修复后DataFrame失败: {e}，返回原始数据")
            return original_df, all_issues

    # ==================== 主要校验方法 ====================

    # ==================== 工具方法 ====================
    
    def get_optimal_thread_count(self, module_count: int) -> int:
        """获取最优线程数"""
        if THREAD_COUNT > 0:
            optimal_threads = min(THREAD_COUNT, module_count)
        else:
            cpu_count = os.cpu_count() or 4
            optimal_threads = min(cpu_count, module_count)
        
        optimal_threads = min(optimal_threads, MAX_THREAD_COUNT)
        return max(1, optimal_threads)

    def validate_file(self, input_file: str, output_file: str = None) -> Dict:
        """分离式校验整个文件"""
        print(f"开始分离式校验文件: {input_file}")
        start_time = time.time()

        # 解析文件
        df = self.parse_excel_file(input_file)
        if df is None:
            return {"error": "文件解析失败"}

        print(f"原始数据: {len(df)} 行")

        # ==================== A. Python程序检查 ====================
        python_check_result = self.run_python_checks(df)

        # ==================== B. LLM检查和修复 ====================
        fixed_df, llm_issues = self.run_llm_checks_and_fixes(df)

        # ==================== C. 合并结果并输出 ====================
        processing_time = time.time() - start_time

        # 汇总所有结果
        final_result = {
            "input_file": input_file,
            "processing_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration_seconds": processing_time,
            "original_records": len(df),
            "fixed_records": len(fixed_df),
            "python_checks": python_check_result,
            "llm_issues": llm_issues,
            "summary": {
                "python_issues_found": python_check_result["total_issues"],
                "llm_issues_found": len(llm_issues),
                "total_issues": python_check_result["total_issues"] + len(llm_issues),
                "data_changed": len(fixed_df) != len(df)
            }
        }

        # 保存结果
        self._save_results(final_result, fixed_df, output_file)

        print(f"分离式校验完成，耗时: {processing_time:.2f}秒")
        print(f"Python检查发现 {python_check_result['total_issues']} 个问题")
        print(f"LLM修复发现 {len(llm_issues)} 个问题")

        return final_result



    def _save_results(self, result: Dict, fixed_df: pd.DataFrame, output_file: str = None):
        """保存分离式校验结果"""
        # 保存完整结果
        result_file = os.path.join(self.output_dir, CHECK_RESULT_FILE)
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {result_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")

        # 保存修复后的数据到Excel/CSV
        if output_file is None:
            timestamp = time.strftime("%m%d%H%M")
            output_file = os.path.join(self.output_dir, f"cosmic_validated_{timestamp}.xlsx")

        try:
            if len(fixed_df) > 0:
                if output_file.endswith('.csv'):
                    fixed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
                else:
                    fixed_df.to_excel(output_file, index=False)

                print(f"修复后的数据已保存到: {output_file}")
            else:
                print("没有修复数据需要保存")
        except Exception as e:
            print(f"保存修复数据失败: {e}")

        # 生成校验报告
        self._generate_report(result)

    def _generate_report(self, result: Dict):
        """生成分离式校验报告"""
        report_file = os.path.join(self.output_dir, "cosmic_validation_report.txt")

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("COSMIC分离式校验报告\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"输入文件: {result.get('input_file', 'N/A')}\n")
                f.write(f"处理时间: {result.get('processing_time', 'N/A')}\n")
                f.write(f"处理耗时: {result.get('duration_seconds', 0):.2f} 秒\n\n")

                # 汇总信息
                f.write("汇总信息:\n")
                f.write(f"  原始记录数: {result.get('original_records', 0)}\n")
                f.write(f"  修复记录数: {result.get('fixed_records', 0)}\n")

                summary = result.get('summary', {})
                f.write(f"  Python检查问题: {summary.get('python_issues_found', 0)}\n")
                f.write(f"  LLM修复问题: {summary.get('llm_issues_found', 0)}\n")
                f.write(f"  总问题数: {summary.get('total_issues', 0)}\n")
                f.write(f"  数据是否变更: {'是' if summary.get('data_changed', False) else '否'}\n\n")

                # Python检查详情
                python_checks = result.get('python_checks', {})
                f.write("Python程序检查详情:\n")
                f.write(f"  总问题数: {python_checks.get('total_issues', 0)}\n")

                issue_types = python_checks.get('issue_types', {})
                if issue_types:
                    f.write("  问题类型分布:\n")
                    for issue_type, count in issue_types.items():
                        f.write(f"    {issue_type}: {count} 个\n")

                # Python检查问题详情
                python_issues = python_checks.get('issues', [])
                if python_issues:
                    f.write("\n  Python检查发现的问题:\n")
                    for i, issue in enumerate(python_issues[:10]):  # 只显示前10个
                        f.write(f"    {i+1}. {issue.get('issue_type', '未知')}: {issue.get('description', 'N/A')}\n")
                        f.write(f"       影响行: {issue.get('affected_rows', [])}\n")
                    if len(python_issues) > 10:
                        f.write(f"    ... 还有 {len(python_issues) - 10} 个问题\n")

                # LLM修复详情
                llm_issues = result.get('llm_issues', [])
                f.write(f"\nLLM修复详情:\n")
                f.write(f"  修复问题数: {len(llm_issues)}\n")

                if llm_issues:
                    llm_issue_types = {}
                    for issue in llm_issues:
                        issue_type = issue.get('issue_type', '未知')
                        llm_issue_types[issue_type] = llm_issue_types.get(issue_type, 0) + 1

                    f.write("  LLM修复问题类型分布:\n")
                    for issue_type, count in llm_issue_types.items():
                        f.write(f"    {issue_type}: {count} 个\n")

            print(f"校验报告已保存到: {report_file}")
        except Exception as e:
            print(f"生成校验报告失败: {e}")


def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = "data/附件4：甘肃移动-软件功能-test2.xlsx"

    # 输出文件处理
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    if not output_file:
        # 生成默认输出文件名
        base_name = os.path.splitext(input_file)[0]
        output_file = base_name + "_修复.csv"

    print("COSMIC分离式校验器")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("=" * 50)

    # 创建校验器
    validator = CosmicValidator()

    # 执行分离式校验
    result = validator.validate_file(input_file, output_file)
    process_csv_to_excel(output_file)

    if "error" in result:
        print(f"校验失败: {result['error']}")
    else:
        print("\n" + "=" * 50)
        print("分离式校验完成！")
        print("=" * 50)

        summary = result.get('summary', {})
        print(f"Python检查问题: {summary.get('python_issues_found', 0)}")
        print(f"LLM修复问题: {summary.get('llm_issues_found', 0)}")
        print(f"总问题数: {summary.get('total_issues', 0)}")
        print(f"数据是否变更: {'是' if summary.get('data_changed', False) else '否'}")
        print(f"\n详细报告请查看: {validator.output_dir}/cosmic_validation_report.txt")


if __name__ == "__main__":
    main()
