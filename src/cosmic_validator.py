#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验器

对main.py生成的cosmic功能拆解excel文件进行校验，检查以下问题：
1. 不同行的数据属性必须不能相同
2. 不同行的功能过程、子过程不能完全相同  
3. 每个功能过程的数据移动类型的第一步必须是E，最后一步必须是W或者X，不能有连续的E
4. 数据属性必须用中文，可以有中文+英文缩写，如：操作日志ID
5. 数据组的内容需要包含三级模块中的主要实体名称

使用大模型批量检查和修复，支持多线程并行处理。
"""

import pandas as pd
import json
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict, OrderedDict
from typing import Dict, List, Any, Tuple
import sys

# 添加src目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import llm_util
from config import (
    CHECK_ENDPOINT_URL, CHECK_MODEL_NAME, CHECK_API_KEY, 
    CHECK_API_QPM, CHECK_API_TPM, CHECK_EXCLUDED_FIELDS,
    CHECK_OUTPUT_DIR, CHECK_INPUT_DEBUG_FILE, CHECK_RESULT_FILE,
    CHECK_PROMPT_FILE, CHECK_BATCH_COUNT, THREAD_COUNT, MAX_THREAD_COUNT
)


class CosmicValidator:
    """COSMIC校验器类"""
    
    def __init__(self):
        """初始化校验器"""
        self.prompt = self._load_prompt()
        self.excluded_fields = CHECK_EXCLUDED_FIELDS or []
        self.batch_count = CHECK_BATCH_COUNT or 500
        self.output_dir = CHECK_OUTPUT_DIR or "debug"
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"COSMIC校验器初始化完成")
        print(f"- 批次大小: {self.batch_count}")
        print(f"- 输出目录: {self.output_dir}")
        print(f"- 排除字段: {self.excluded_fields}")
    
    def _load_prompt(self) -> str:
        """加载校验提示词"""
        try:
            with open(CHECK_PROMPT_FILE, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print(f"警告: 找不到提示词文件 {CHECK_PROMPT_FILE}，使用默认提示词")
            return self._get_default_prompt()
        except Exception as e:
            print(f"加载提示词失败: {e}，使用默认提示词")
            return self._get_default_prompt()
    
    def _get_default_prompt(self) -> str:
        """获取默认提示词"""
        return """你是COSMIC评审专家，负责校验软件功能拆解的规范性。请检查并修复数据中的问题，严格按照JSON格式输出结果。"""
    
    def parse_excel_file(self, file_path: str) -> pd.DataFrame:
        """解析Excel或CSV文件"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path)
            
            print(f"成功解析文件: {file_path}")
            print(f"数据行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            
            return df
        except Exception as e:
            print(f"解析文件失败: {e}")
            return None
    
    def group_by_level2_module(self, df: pd.DataFrame) -> Dict[str, List[Dict]]:
        """按二级模块分组数据"""
        grouped_data = defaultdict(list)
        
        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row.get('三级功能模块')) or str(row.get('三级功能模块')).strip() == '':
                continue
            
            level2_module = str(row.get('二级功能模块', ''))
            
            # 转换为字典，排除指定字段
            row_dict = {}
            for col, value in row.items():
                if col not in self.excluded_fields:
                    row_dict[col] = value if not pd.isna(value) else ""
            
            grouped_data[level2_module].append(row_dict)
        
        print(f"按二级模块分组完成，共 {len(grouped_data)} 个模块")
        for module, data in grouped_data.items():
            print(f"  {module}: {len(data)} 条记录")
        
        return dict(grouped_data)
    
    def _call_llm_for_validation(self, data_batch: List[Dict], batch_id: str) -> Dict:
        """调用大模型进行校验"""
        # 构建用户输入
        user_input = f"请校验以下COSMIC数据（批次{batch_id}）：\n\n"
        user_input += json.dumps(data_batch, ensure_ascii=False, indent=2)

        try:
            # 调用LLM，使用默认配置
            result = llm_util.call_LLM(self.prompt, user_input + " /nothink")

            if result:
                # 解析JSON结果
                parsed_result = llm_util.extract_json_from_content(result)
                if parsed_result:
                    return parsed_result
                else:
                    print(f"批次 {batch_id} JSON解析失败")
                    return {"error": "JSON解析失败", "raw_content": result}
            else:
                print(f"批次 {batch_id} LLM调用失败")
                return {"error": "LLM调用失败"}

        except Exception as e:
            print(f"批次 {batch_id} 处理异常: {e}")
            return {"error": str(e)}
    
    def validate_level2_module(self, module_name: str, module_data: List[Dict]) -> Tuple[str, Dict]:
        """校验单个二级模块的数据"""
        print(f"[线程{threading.current_thread().name}] 开始校验模块: {module_name}")
        
        # 分批处理
        batches = []
        for i in range(0, len(module_data), self.batch_count):
            batch = module_data[i:i + self.batch_count]
            batches.append(batch)
        
        print(f"[线程{threading.current_thread().name}] 模块 {module_name} 分为 {len(batches)} 个批次")
        
        # 处理每个批次
        all_fixed_data = []
        all_issues = []
        total_issues = 0
        fixed_records = 0
        
        for batch_idx, batch in enumerate(batches):
            batch_id = f"{module_name}_batch_{batch_idx + 1}"
            
            # 保存输入数据用于调试
            debug_file = os.path.join(self.output_dir, f"{batch_id}_input.json")
            try:
                with open(debug_file, 'w', encoding='utf-8') as f:
                    json.dump(batch, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"保存调试文件失败: {e}")
            
            # 调用LLM校验
            result = self._call_llm_for_validation(batch, batch_id)
            
            if "error" in result:
                print(f"批次 {batch_id} 校验失败: {result['error']}")
                # 如果校验失败，保留原始数据
                all_fixed_data.extend(batch)
            else:
                # 处理校验结果
                validation_summary = result.get('validation_summary', {})
                fixed_data = result.get('fixed_data', batch)
                issues = result.get('issues_found', [])
                
                total_issues += validation_summary.get('total_issues', 0)
                fixed_records += validation_summary.get('fixed_records', 0)
                
                all_fixed_data.extend(fixed_data)
                all_issues.extend(issues)
                
                print(f"批次 {batch_id} 校验完成: 发现 {validation_summary.get('total_issues', 0)} 个问题，修复 {validation_summary.get('fixed_records', 0)} 条记录")
        
        # 汇总结果
        module_result = {
            "module_name": module_name,
            "original_count": len(module_data),
            "fixed_count": len(all_fixed_data),
            "total_issues": total_issues,
            "fixed_records": fixed_records,
            "compliance_rate": f"{((len(module_data) - total_issues) / len(module_data) * 100):.1f}%" if len(module_data) > 0 else "100%",
            "fixed_data": all_fixed_data,
            "issues_found": all_issues
        }
        
        print(f"[线程{threading.current_thread().name}] 完成模块 {module_name}: 原始{len(module_data)}条，修复{len(all_fixed_data)}条，问题{total_issues}个")
        
        return module_name, module_result
    
    def get_optimal_thread_count(self, module_count: int) -> int:
        """获取最优线程数"""
        if THREAD_COUNT > 0:
            optimal_threads = min(THREAD_COUNT, module_count)
        else:
            cpu_count = os.cpu_count() or 4
            optimal_threads = min(cpu_count, module_count)
        
        optimal_threads = min(optimal_threads, MAX_THREAD_COUNT)
        return max(1, optimal_threads)

    def validate_file(self, input_file: str, output_file: str = None) -> Dict:
        """校验整个文件"""
        print(f"开始校验文件: {input_file}")

        # 解析文件
        df = self.parse_excel_file(input_file)
        if df is None:
            return {"error": "文件解析失败"}

        # 按二级模块分组
        grouped_data = self.group_by_level2_module(df)
        if not grouped_data:
            return {"error": "没有有效数据"}

        # 确定线程数
        thread_count = self.get_optimal_thread_count(len(grouped_data))
        print(f"使用 {thread_count} 个线程进行校验")

        # 多线程校验
        start_time = time.time()
        all_results = {}

        with ThreadPoolExecutor(max_workers=thread_count, thread_name_prefix="CosmicValidator") as executor:
            # 提交任务
            future_to_module = {}
            for module_name, module_data in grouped_data.items():
                future = executor.submit(self.validate_level2_module, module_name, module_data)
                future_to_module[future] = module_name

            # 收集结果
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    result_module_name, result = future.result()
                    all_results[result_module_name] = result
                except Exception as exc:
                    print(f"模块 {module_name} 校验失败: {exc}")
                    all_results[module_name] = {"error": str(exc)}

        processing_time = time.time() - start_time
        print(f"校验完成，耗时: {processing_time:.2f}秒")

        # 汇总所有结果
        final_result = self._merge_results(all_results, input_file)

        # 保存结果
        self._save_results(final_result, output_file)

        return final_result

    def _merge_results(self, all_results: Dict, input_file: str) -> Dict:
        """合并所有模块的校验结果"""
        all_fixed_data = []
        all_issues = []
        total_original = 0
        total_fixed = 0
        total_issues = 0
        total_fixed_records = 0

        for module_name, result in all_results.items():
            if "error" in result:
                print(f"模块 {module_name} 有错误: {result['error']}")
                continue

            all_fixed_data.extend(result.get('fixed_data', []))
            all_issues.extend(result.get('issues_found', []))
            total_original += result.get('original_count', 0)
            total_fixed += result.get('fixed_count', 0)
            total_issues += result.get('total_issues', 0)
            total_fixed_records += result.get('fixed_records', 0)

        # 计算总体合规率
        compliance_rate = f"{((total_original - total_issues) / total_original * 100):.1f}%" if total_original > 0 else "100%"

        final_result = {
            "input_file": input_file,
            "processing_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total_modules": len(all_results),
                "total_original_records": total_original,
                "total_fixed_records": total_fixed,
                "total_issues_found": total_issues,
                "records_with_fixes": total_fixed_records,
                "overall_compliance_rate": compliance_rate
            },
            "module_results": all_results,
            "fixed_data": all_fixed_data,
            "all_issues": all_issues
        }

        print(f"校验汇总:")
        print(f"  总模块数: {len(all_results)}")
        print(f"  原始记录: {total_original}")
        print(f"  修复记录: {total_fixed}")
        print(f"  发现问题: {total_issues}")
        print(f"  合规率: {compliance_rate}")

        return final_result

    def _save_results(self, result: Dict, output_file: str = None):
        """保存校验结果"""
        # 保存完整结果
        result_file = os.path.join(self.output_dir, CHECK_RESULT_FILE)
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"校验结果已保存到: {result_file}")
        except Exception as e:
            print(f"保存校验结果失败: {e}")

        # 保存修复后的数据到Excel/CSV
        if output_file is None:
            timestamp = time.strftime("%m%d%H%M")
            output_file = os.path.join(self.output_dir, f"cosmic_validated_{timestamp}.xlsx")

        try:
            fixed_data = result.get('fixed_data', [])
            if fixed_data:
                df_fixed = pd.DataFrame(fixed_data)

                if output_file.endswith('.csv'):
                    df_fixed.to_csv(output_file, index=False, encoding='utf-8-sig')
                else:
                    df_fixed.to_excel(output_file, index=False)

                print(f"修复后的数据已保存到: {output_file}")
            else:
                print("没有修复数据需要保存")
        except Exception as e:
            print(f"保存修复数据失败: {e}")

        # 生成校验报告
        self._generate_report(result)

    def _generate_report(self, result: Dict):
        """生成校验报告"""
        report_file = os.path.join(self.output_dir, "cosmic_validation_report.txt")

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("COSMIC校验报告\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"输入文件: {result.get('input_file', 'N/A')}\n")
                f.write(f"处理时间: {result.get('processing_time', 'N/A')}\n\n")

                # 汇总信息
                summary = result.get('summary', {})
                f.write("汇总信息:\n")
                f.write(f"  总模块数: {summary.get('total_modules', 0)}\n")
                f.write(f"  原始记录数: {summary.get('total_original_records', 0)}\n")
                f.write(f"  修复记录数: {summary.get('total_fixed_records', 0)}\n")
                f.write(f"  发现问题数: {summary.get('total_issues_found', 0)}\n")
                f.write(f"  整体合规率: {summary.get('overall_compliance_rate', 'N/A')}\n\n")

                # 各模块详情
                f.write("各模块详情:\n")
                module_results = result.get('module_results', {})
                for module_name, module_result in module_results.items():
                    if "error" in module_result:
                        f.write(f"  {module_name}: 错误 - {module_result['error']}\n")
                    else:
                        f.write(f"  {module_name}:\n")
                        f.write(f"    原始记录: {module_result.get('original_count', 0)}\n")
                        f.write(f"    发现问题: {module_result.get('total_issues', 0)}\n")
                        f.write(f"    修复记录: {module_result.get('fixed_records', 0)}\n")
                        f.write(f"    合规率: {module_result.get('compliance_rate', 'N/A')}\n")

                # 问题统计
                all_issues = result.get('all_issues', [])
                if all_issues:
                    f.write(f"\n发现的问题类型统计:\n")
                    issue_types = {}
                    for issue in all_issues:
                        issue_type = issue.get('issue_type', '未知')
                        issue_types[issue_type] = issue_types.get(issue_type, 0) + 1

                    for issue_type, count in issue_types.items():
                        f.write(f"  {issue_type}: {count} 个\n")

            print(f"校验报告已保存到: {report_file}")
        except Exception as e:
            print(f"生成校验报告失败: {e}")


def main():
    """主函数"""
    import sys

    if len(sys.argv) > 1:
        input_file = sys.argv[1] # COSMIC数据文件
    else:
        input_file = "data/附件2：功能清单-2025_qwen3_coder_plus.xlsx"
    # 没有提供输出文件，在输入文件名+"_修复"后缀
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    output_file = input_file + "_修复.xlsx" if not output_file else output_file
    

    # 创建校验器
    validator = CosmicValidator()

    # 执行校验
    result = validator.validate_file(input_file, output_file)

    if "error" in result:
        print(f"校验失败: {result['error']}")
    else:
        print("校验完成！")


if __name__ == "__main__":
    main()
