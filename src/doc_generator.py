#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能需求文档生成器

基于COSMIC功能拆解结果生成功能需求文档，包括：
1. 功能需求描述
2. 关键时序图（Mermaid语法）
3. 分批次处理，每批次最多50个子过程
4. 支持配置起始序号
"""

import pandas as pd
import json
import os
import time
import re
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Tuple
from collections import defaultdict
import llm_util
from knowledge_base import knowledge_base
from config import KNOWLEDGE_BASE_ENABLED, REQUIREMENT_GENERATOR_CONFIG


class RequirementGenerator:
    """功能需求文档生成器"""
    
    def __init__(self, start_number: str = None, max_subprocess_per_batch: int = None):
        """
        初始化生成器

        Args:
            start_number: 起始序号，如"2"表示从2.1开始，"3.1"表示从3.1.1开始
            max_subprocess_per_batch: 每批次最大子过程数量
        """
        # 使用配置文件中的默认值
        self.config = REQUIREMENT_GENERATOR_CONFIG
        self.start_number = start_number or self.config["default_start_number"]
        self.max_subprocess_per_batch = max_subprocess_per_batch or self.config["max_subprocess_per_batch"]
        self.prompt_file = self.config["prompt_file"]
        #self.output_dir = self.config["output_dir"]
        self.column_mapping = self.config["column_mapping"]
        
        # 加载系统提示词
        self.load_prompt()
        
        # 序号计数器
        self.level1_counter = 1
        self.level2_counter = 1
        self.level3_counter = 1
        self.function_counter = 1
        
        # 解析起始序号
        self.parse_start_number()

        # 多线程相关
        self.max_threads = self.config.get("max_threads", 4)  # 最大线程数
        self.counter_lock = threading.Lock()  # 序号计数器锁
        self.result_lock = threading.Lock()   # 结果收集锁
    
    def load_prompt(self):
        """加载系统提示词"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
            print(f"成功加载系统提示词: {self.prompt_file}")
        except FileNotFoundError:
            print(f"警告: 系统提示词文件 {self.prompt_file} 不存在")
            self.system_prompt = "你是一位资深的软件需求分析师，请根据COSMIC功能拆解数据生成功能需求文档。"
    
    def parse_start_number(self):
        """解析起始序号"""
        parts = self.start_number.split('.')
        if len(parts) == 1:
            # 如"2"，表示从2.1开始
            self.base_number = parts[0]
            self.level1_counter = 1
        elif len(parts) == 2:
            # 如"3.1"，表示从3.1.1开始
            self.base_number = parts[0]
            self.level1_counter = int(parts[1])
        else:
            # 默认处理
            self.base_number = "2"
            self.level1_counter = 1
    
    def load_cosmic_data(self, file_path: str) -> pd.DataFrame:
        """
        加载COSMIC功能拆解数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame: 加载的数据
        """
        try:
            # 检测文件类型
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            elif file_extension == '.xlsx':
                # 读取XLS文件内容
                sheet_name = 0
                header = 0
                df = pd.read_excel(file_path, sheet_name = sheet_name, header = header)
                # 向后填充有合并的数据列
                level_1, level_2, level_3, func_user,event_name,func_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
                df[[level_1, level_2, level_3, func_user,event_name,func_name]] = df[[level_1, level_2, level_3, func_user,event_name,func_name]].ffill()            
            
            print(f"成功加载COSMIC数据行数: {len(df)}")
            return df
        except Exception as e:
            print(f"加载COSMIC数据失败: {e}")
            return pd.DataFrame()
    
    def organize_data_by_hierarchy(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        按层级结构组织数据

        Args:
            df: 原始数据DataFrame

        Returns:
            Dict: 按层级组织的数据结构
        """
        # 使用配置文件中的列名映射
        level1_col = self.column_mapping["level1"]
        level2_col = self.column_mapping["level2"]
        level3_col = self.column_mapping["level3"]
        function_col = self.column_mapping["function"]
        
        # 检查必要列是否存在
        required_cols = [level1_col, level2_col, level3_col, function_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"警告: 缺少必要列: {missing_cols}")
            print(f"可用列: {list(df.columns)}")
            return {}
        
        # 组织数据结构
        hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        
        for _, row in df.iterrows():
            level1 = str(row[level1_col]).strip()
            level2 = str(row[level2_col]).strip()
            level3 = str(row[level3_col]).strip()
            
            # 跳过空值
            if pd.isna(level1) or pd.isna(level2) or pd.isna(level3):
                continue
            if level1.lower() in ['nan', 'none', ''] or level2.lower() in ['nan', 'none', ''] or level3.lower() in ['nan', 'none', '']:
                continue
            
            # 将整行数据添加到对应的三级模块下
            hierarchy[level1][level2][level3].append(row.to_dict())
        
        return dict(hierarchy)
    
    def get_knowledge_context(self, batch: Dict[str, Any]) -> str:
        """
        获取批次相关的知识库上下文
        
        Args:
            batch: 批次数据
            
        Returns:
            str: 知识库上下文
        """
        if not KNOWLEDGE_BASE_ENABLED or not knowledge_base.enabled:
            return ""
        
        # 收集所有相关的查询词
        query_parts = []
        for level1, level2_data in batch.items():
            for level2, level3_data in level2_data.items():
                for level3, functions in level3_data.items():
                    query_parts.extend([level1, level2, level3])
                    for func_data in functions:
                        if func_data.get('功能过程'):
                            query_parts.append(func_data['功能过程'])
        
        # 构建查询
        query = " ".join(set(query_parts))
        context = knowledge_base.get_context_for_module(query)
        
        if context:
            print(f"获取到知识库上下文: {len(context)} 字符")
        
        return context    
    def generate_level1_description_from_llm(self, level1_name: str, level1_data: Dict[str, Any]) -> str:
        """
        使用LLM为单个一级模块生成功能描述

        Args:
            level1_name: 一级模块名称
            level1_data: 一级模块数据

        Returns:
            str: 一级模块的功能描述
        """
        print(f"开始为一级模块 '{level1_name}' 生成功能描述...")

        system_prompt = """
# 角色

你是一位资深的软件需求分析师和技术文档专家，具备丰富的软件系统分析、业务流程设计和技术文档编写经验。你熟悉COSMIC功能点分析方法，能够基于COSMIC功能拆解结果生成高质量的功能需求文档。
# 技能

你具备以下关键能力：
- **需求分析能力**：能够从COSMIC功能拆解中提取核心业务需求和功能特性
# 任务

用户提供了基于COSMIC方法拆解的功能点数据，包含一级模块、二级模块、三级模块的详细信息。你需要基于这些数据生成JSON格式的输出，包括：
1. **一级模块功能描述**：基于该一级模块下的所有二级模块和三级模块的功能名称，生成清晰的功能需求描述
2. **JSON格式输出**：将功能简介和功能描述以结构化的JSON格式输出，方便后续处理
# 输出
请按照以下格式输出：
```json
{
    "功能简介": "一级模块的功能简介及主要功能列表",
    "功能描述": "综合分析一级模块的各二级模块功能，并给出详细文字描述，不要结构化输出"
}
```
        """    
        # 格式化输入数据
        user_input = self.format_level1_description_for_llm(level1_name, level1_data)

        # 获取知识库上下文
        knowledge_context = ""  # self.get_knowledge_context({level1_name: level1_data})

        # 调用大模型生成
        try:
            result = llm_util.call_LLM(system_prompt, user_input, knowledge_context)
            print(f"一级模块 '{level1_name}' 功能描述生成完成")

            # 去除<think> 和 </think>之间的内容
            result = llm_util.remove_think(result)

            # 解析结果
            json_rsult = llm_util.extract_json_from_content(result)
            return json_rsult

        except Exception as e:
            print(f"一级模块 '{level1_name}' 功能描述生成失败: {e}")
            return f"{level1_name}模块功能描述生成失败: {str(e)}"

    def _generate_sequence_diagrams(self, level1_name: str, batch: Dict[str, Any]) -> Dict[str, str]:
        """
        使用LLM为三级模块生成时序图

        Args:
            level1_name: 一级模块名称
            batch: 三级模块数据 {level3_name: functions_data}

        Returns:
            Dict[str, str]: 三级模块时序图映射 {level3_name: sequence_diagram}
        """
        # 格式化输入数据
        user_input = self.format_level3_sequence_for_llm(level1_name, batch)

        # 获取知识库上下文
        knowledge_context = ""  # self.get_knowledge_context({level1_name: level3_modules})

        # 调用大模型生成
        try:
            result = llm_util.call_LLM(self.system_prompt, user_input, knowledge_context)
            print(f"一级模块 '{level1_name}' 的时序图生成完成")

            # 去除<think> 和 </think>之间的内容
            result = llm_util.remove_think(result)

            # 解析时序图结果
            sequence_diagrams = self.parse_sequence_diagrams_result(result)
            return sequence_diagrams

        except Exception as e:
            print(f"一级模块 '{level1_name}' 时序图生成失败: {e}")
            return {}
    def generate_level3_sequence_diagrams_from_llm(self, level1_name: str, level3_modules: Dict[str, Any]) -> Dict[str, str]:
        """
        使用LLM为三级模块生成时序图

        Args:
            level1_name: 一级模块名称
            level3_modules: 三级模块数据 {level3_name: functions_data}

        Returns:
            Dict[str, str]: 三级模块时序图映射 {level3_name: sequence_diagram}
        """
        print(f"开始为一级模块 '{level1_name}' 的三级模块生成时序图...")

        # level3_modules 批次处理，不能超过max_subprocess_per_batch
        sequence_diagrams = {}
        batch = {}
        batch_idx = 1
        for level3_name, functions_data in level3_modules.items():
            batch[level3_name] = functions_data
            if len(batch) >= self.max_subprocess_per_batch:
                print(f"          正在处理第 {batch_idx} 批次...")
                results = self._generate_sequence_diagrams(level1_name, batch)
                # 把results添加到sequence_diagrams中
                sequence_diagrams.update(results)
                batch = {}
                batch_idx += 1
        
        if len(batch) > 0:
            print(f"          正在处理第 {batch_idx} 批次...")
            results = self._generate_sequence_diagrams(level1_name, batch)
            sequence_diagrams.update(results)

        return sequence_diagrams

    def format_level1_description_for_llm(self, level1_name: str, level1_data: Dict[str, Any]) -> str:
        """
        将一级模块数据格式化为LLM输入（用于生成功能描述）

        Args:
            level1_name: 一级模块名称
            level1_data: 一级模块数据

        Returns:
            str: 格式化的输入文本
        """
        lines = []
        lines.append(f"请为一级模块 '{level1_name}' 生成功能简介和功能描述：")
        lines.append("")
        lines.append("该一级模块包含以下内容：")

        # 遍历二级模块
        for level2_name, level2_data in level1_data.items():
            lines.append(f"二级模块：{level2_name}")

            # 遍历三级模块
            for level3_name, _ in level2_data.items():
                lines.append(f"  三级模块：{level3_name}")

                # # 遍历功能过程
                # function_names = set()
                # for function_data in functions:
                #     function_name = function_data.get(self.column_mapping["function"], "")
                #     if function_name:
                #         function_names.add(function_name)

                # for func_name in sorted(function_names):
                #     lines.append(f"    功能过程：{func_name}")

        # lines.append("")
        # lines.append("请生成该一级模块的整体功能描述，包括：")
        # lines.append("1. 该模块的主要业务目的和价值")
        # lines.append("2. 主要的输入、处理逻辑和输出")
        # lines.append("3. 该模块在整个系统中的作用")
        # lines.append("")
        # lines.append("请直接输出功能描述文本，不需要JSON格式。")

        return "\n".join(lines)

    def format_level3_sequence_for_llm(self, level1_name: str, level3_modules: Dict[str, Any]) -> str:
        """
        将三级模块数据格式化为LLM输入（用于生成时序图）

        Args:
            level1_name: 一级模块名称
            level3_modules: 三级模块数据

        Returns:
            str: 格式化的输入文本
        """
        lines = []
        lines.append(f"请为一级模块 '{level1_name}' 的各个三级模块生成时序图：")
        lines.append("")

        for level3_name, functions in level3_modules.items():
            lines.append(f"三级模块：{level3_name}")

            # 遍历功能过程
            function_names = set()
            for function_data in functions:
                function_name = function_data.get(self.column_mapping["function"], "")
                if function_name:
                    function_names.add(function_name)

            for func_name in sorted(function_names):
                lines.append(f"  功能过程：{func_name}")

                # 获取该功能过程的所有子过程
                subprocesses = []
                for function_data in functions:
                    if function_data.get(self.column_mapping["function"]) == func_name:
                        subprocess = function_data.get(self.column_mapping["subprocess"], "")
                        trigger = function_data.get(self.column_mapping["trigger"], "")
                        user = function_data.get(self.column_mapping["user"], "")
                        data_movement = function_data.get(self.column_mapping["data_movement"], "")

                        if subprocess:
                            subprocesses.append({
                                "subprocess": subprocess,
                                "trigger": trigger,
                                "user": user,
                                "data_movement": data_movement
                            })

                # 显示子过程信息
                for i, sp in enumerate(subprocesses, 1):
                    lines.append(f"    {i}. {sp['subprocess']} ({sp['data_movement']})")
                    if sp['trigger']:
                        lines.append(f"       触发事件：{sp['trigger']}")
                    if sp['user']:
                        lines.append(f"       功能用户：{sp['user']}")

            lines.append("")

        lines.append("请生成JSON格式的输出，包含每个三级模块的时序图：")
        lines.append('{"level3_sequence_diagrams": [{"level3_module_name": "模块名", "sequence_diagram": "Mermaid代码"}]}')

        return "\n".join(lines)

    def parse_sequence_diagrams_result(self, result: str) -> Dict[str, str]:
        """
        解析LLM返回的时序图结果

        Args:
            result: LLM返回的文本

        Returns:
            Dict[str, str]: 三级模块时序图映射
        """
        try:
            # 尝试直接解析JSON
            json_result = json.loads(result)
            sequence_diagrams = {}

            if "level3_sequence_diagrams" in json_result:
                for item in json_result["level3_sequence_diagrams"]:
                    level3_name = item.get("level3_module_name", "")
                    sequence_diagram = item.get("sequence_diagram", "")
                    if level3_name and sequence_diagram:
                        sequence_diagrams[level3_name] = sequence_diagram

            return sequence_diagrams

        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            json_match = re.search(r'\{.*\}', result, re.DOTALL)
            if json_match:
                try:
                    json_result = json.loads(json_match.group())
                    sequence_diagrams = {}

                    if "level3_sequence_diagrams" in json_result:
                        for item in json_result["level3_sequence_diagrams"]:
                            level3_name = item.get("level3_module_name", "")
                            sequence_diagram = item.get("sequence_diagram", "")
                            if level3_name and sequence_diagram:
                                sequence_diagrams[level3_name] = sequence_diagram

                    return sequence_diagrams
                except json.JSONDecodeError:
                    pass

            # 如果都失败了，返回空字典
            print(f"时序图JSON解析失败，原始结果：{result[:200]}...")
            return {}

    def _process_level1_module(self, level1_name: str, level1_data: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, str]]:
        """
        处理单个一级模块的功能描述和时序图生成（多线程调用）

        Args:
            level1_name: 一级模块名称
            level1_data: 一级模块数据

        Returns:
            Tuple[Dict[str, Any], Dict[str, str]]: (功能描述, 时序图映射)
        """
        print(f"[线程] 开始处理一级模块: {level1_name}")

        try:
            # 第一步：生成功能描述
            print(f"[线程] 正在生成一级模块功能描述: {level1_name}")
            description = self.generate_level1_description_from_llm(level1_name, level1_data)

            # 第二步：生成时序图
            print(f"[线程] 正在生成一级模块 '{level1_name}' 的时序图")

            # 收集该一级模块下的所有三级模块
            level3_modules = {}
            for _, level2_data in level1_data.items():
                for level3_name, functions in level2_data.items():
                    level3_modules[level3_name] = functions

            # 生成时序图
            sequence_diagrams = self.generate_level3_sequence_diagrams_from_llm(level1_name, level3_modules)

            print(f"[线程] 一级模块 '{level1_name}' 处理完成")
            return description, sequence_diagrams

        except Exception as e:
            print(f"[线程] 一级模块 '{level1_name}' 处理失败: {e}")
            raise e

    def organize_document_from_dataframe(self, hierarchy: Dict[str, Any], llm_results: Dict[str, Dict[str, Any]]) -> str:
        """
        根据DataFrame数据和LLM结果组织完整的文档结构

        Args:
            hierarchy: 按层级组织的数据
            llm_results: LLM生成的结果，key为一级模块名称

        Returns:
            str: 完整的Markdown文档
        """
        lines = []
        level1_counter = 1

        for level1_name, level1_data in hierarchy.items():
            # 获取LLM生成的内容
            llm_content = llm_results.get(level1_name, {})
            level1_description = llm_content.get("level1_description", f"{level1_name}模块功能描述")
            sequence_diagrams = llm_content.get("level3_sequence_diagrams", [])

            # 创建三级模块时序图映射
            sequence_diagram_map = {}
            for seq_diag in sequence_diagrams:
                level3_name = seq_diag.get("level3_module_name", "")
                sequence_diagram_map[level3_name] = seq_diag.get("sequence_diagram", "")

            # 生成一级模块标题和描述
            lines.append(f"## {self.base_number}.{level1_counter} {level1_name}")
            lines.append(level1_description.get("功能简介",""))
            lines.append("")

            # 生成关键时序图部分
            lines.append(f"### {self.base_number}.{level1_counter}.1 关键时序图/业务逻辑图")

            # 为每个三级模块生成时序图
            seq_counter = 1
            for level2_name, level2_data in level1_data.items():
                for level3_name, functions in level2_data.items():
                    sequence_diagram = sequence_diagram_map.get(level3_name, "")
                    if sequence_diagram:
                        lines.append(f"{seq_counter}.{level3_name} - 时序图")
                        lines.append('<div class="mermaid">')
                        lines.append(sequence_diagram)
                        lines.append('</div>')
                        lines.append("")
                        seq_counter += 1

            # 生成功能需求描述部分
            lines.append(f"### {self.base_number}.{level1_counter}.2 功能需求描述")
            lines.append(f"{level1_name}模块的详细功能需求如下：")
            lines.append(level1_description.get("功能描述",""))
            lines.append("")

            # 生成二级模块
            level2_counter = 1
            for level2_name, level2_data in level1_data.items():
                lines.append(f"#### {self.base_number}.{level1_counter}.2.{level2_counter} {level2_name}")
                lines.append("")

                # 生成三级模块
                level3_counter = 1
                for level3_name, functions in level2_data.items():
                    lines.append(f"##### {self.base_number}.{level1_counter}.2.{level2_counter}.{level3_counter} {level3_name}")
                    lines.append(f"{level3_name} 包含如下功能：<br/>")

                    # 获取功能过程列表
                    function_names = set()
                    for function_data in functions:
                        function_name = function_data.get(self.column_mapping["function"], "")
                        if function_name:
                            function_names.add(function_name)

                    # 列出功能过程
                    for i, func_name in enumerate(sorted(function_names), 1):
                        lines.append(f"  {i}.{func_name}<br/>")
                    lines.append("")

                    # 生成功能过程详情
                    function_counter = 1
                    for func_name in sorted(function_names):
                        lines.append(f"###### {self.base_number}.{level1_counter}.2.{level2_counter}.{level3_counter}.{function_counter} {func_name}")
                        lines.append("***功能简介*** <br/>")
                        lines.append(f"   {func_name}<br/>")
                        lines.append("***功能要求*** <br/>")

                        # 获取该功能过程的所有子过程
                        subprocesses = []
                        for function_data in functions:
                            if function_data.get(self.column_mapping["function"]) == func_name:
                                subprocess = function_data.get(self.column_mapping["subprocess"], "")
                                if subprocess and subprocess not in subprocesses:
                                    subprocesses.append(subprocess)

                        # 列出子过程
                        for i, subprocess in enumerate(subprocesses, 1):
                            lines.append(f"   {i}.{subprocess}<br/>")
                        lines.append("")

                        function_counter += 1

                    level3_counter += 1

                level2_counter += 1

            level1_counter += 1
            lines.append("")

        return "\n".join(lines)
    
    def generate_requirements_document(self, csv_file: str, output_file: str = None) -> str:
        """
        生成完整的功能需求文档，按照新的目录结构

        Args:
            csv_file: COSMIC数据CSV文件路径
            output_file: 输出文件路径，如果为None则自动生成

        Returns:
            str: 输出文件路径
        """
        print("开始生成功能需求文档...")

        # 加载数据
        df = self.load_cosmic_data(csv_file)
        if df.empty:
            print("数据加载失败，无法生成文档")
            return ""

        # 组织数据
        hierarchy = self.organize_data_by_hierarchy(df)
        if not hierarchy:
            print("数据组织失败，无法生成文档")
            return ""

        # 生成输出文件名
        if output_file is None:
            base_name = os.path.splitext(os.path.basename(csv_file))[0]
            output_file = f"{base_name}_功能需求文档.md"

        # 输出目录与文件在同一个目录
        output_path = os.path.join(os.path.dirname(csv_file), output_file)
        # output_path = os.path.join(self.output_dir, output_file)
        # os.makedirs(self.output_dir, exist_ok=True)

        print(f"将处理 {len(hierarchy)} 个一级模块")
        print(f"输出文件: {output_path}")

        # 使用多线程处理一级模块的功能描述和时序图生成
        print("=== 使用多线程生成一级模块功能描述和时序图 ===")
        level1_descriptions = {}
        level3_sequence_diagrams = {}

        # 使用线程池并行处理每个一级模块
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # 提交所有任务
            future_to_level1 = {}
            for level1_name, level1_data in hierarchy.items():
                future = executor.submit(self._process_level1_module, level1_name, level1_data)
                future_to_level1[future] = level1_name

            # 收集结果
            for future in as_completed(future_to_level1):
                level1_name = future_to_level1[future]
                try:
                    description, sequence_diagrams = future.result()
                    with self.result_lock:
                        level1_descriptions[level1_name] = description
                        level3_sequence_diagrams[level1_name] = sequence_diagrams
                    print(f"✓ 一级模块 '{level1_name}' 处理完成")
                except Exception as e:
                    print(f"✗ 一级模块 '{level1_name}' 处理失败: {e}")
                    with self.result_lock:
                        level1_descriptions[level1_name] = f"{level1_name}模块功能描述生成失败: {str(e)}"
                        level3_sequence_diagrams[level1_name] = {}

        # 第三步：组织LLM结果
        llm_results = {}
        for level1_name in hierarchy.keys():
            level1_description = level1_descriptions.get(level1_name, f"{level1_name}模块功能描述")
            sequence_diagrams = level3_sequence_diagrams.get(level1_name, {})

            # 转换为原来的格式
            level3_sequence_list = []
            for level3_name, sequence_diagram in sequence_diagrams.items():
                level3_sequence_list.append({
                    "level3_module_name": level3_name,
                    "sequence_diagram": sequence_diagram
                })

            llm_results[level1_name] = {
                "level1_module_name": level1_name,
                "level1_description": level1_description,
                "level3_sequence_diagrams": level3_sequence_list
            }

        # 第四步：根据DataFrame和LLM结果组织完整文档
        print("=== 第四步：组织文档结构 ===")
        print(f"LLM结果调试信息: {llm_results}")
        final_content = self.organize_document_from_dataframe(hierarchy, llm_results)

        # 添加文档头部
        header_lines = []
        header_lines.append(f"# 功能需求文档")
        header_lines.append(f"")
        header_lines.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        header_lines.append(f"数据来源: {os.path.basename(csv_file)}")
        header_lines.append("")

        final_content = "\n".join(header_lines) + final_content

        # 写入文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_content)
            print(f"✓ 文档已保存到: {output_path}")
        except Exception as e:
            print(f"✗ 文件写入失败: {e}")
            return ""

        print(f"功能需求文档生成完成: {output_path}")
        return output_path
    

def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        csv_file = sys.argv[1] # COSMIC数据文件
    else:
        csv_file = "data/XX研发项目COSMIC_test.xlsx"

    config = REQUIREMENT_GENERATOR_CONFIG
    # 配置参数
    start_number = config["default_start_number"]       # 起始序号，可以是"2"或"3.1"等格式
    max_subprocess_per_batch = config["max_subprocess_per_batch"]  # 每批次最大子过程数

    print("=== 功能需求文档生成器 ===")
    print(f"输入文件: {csv_file}")
    print(f"起始序号: {start_number}")
    print(f"每批次最大子过程数: {max_subprocess_per_batch}")
    print()
    start_time = time.time()

    # 检查输入文件是否存在
    if not os.path.exists(csv_file):
        print(f"❌ 输入文件不存在: {csv_file}")
        print("请确保COSMIC数据文件存在")
        return

    # 创建生成器
    generator = RequirementGenerator(
        start_number=start_number,
        max_subprocess_per_batch=max_subprocess_per_batch
    )

    # 生成文档
    output_file = generator.generate_requirements_document(csv_file)
    #output_file = generator.generate_requirements_document_with_batches(csv_file)

    if output_file:
        print(f"\n✅ 功能需求文档生成成功, 用时{time.time() - start_time} !")
        print(f"📄 输出文件: {output_file}")
        print(f"📊 文件大小: {os.path.getsize(output_file)} 字节")
    else:
        print("\n❌ 功能需求文档生成失败!")


if __name__ == "__main__":
    main()
