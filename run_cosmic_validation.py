#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC校验功能运行脚本

用于运行COSMIC校验功能，对main.py生成的cosmic功能拆解excel文件进行校验
"""

import sys
import os
import argparse
from datetime import datetime

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.cosmic_validator import CosmicValidator


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='COSMIC校验功能')
    parser.add_argument('input_file', help='输入的COSMIC数据文件 (Excel或CSV格式)')
    parser.add_argument('-o', '--output', help='输出文件路径 (可选)')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    
    args = parser.parse_args()
    
    if args.test:
        # 运行测试
        print("运行COSMIC校验功能测试...")
        try:
            import debug.test_cosmic_validation as test_module
            test_module.main()
        except ImportError as e:
            print(f"导入测试模块失败: {e}")
        except Exception as e:
            print(f"测试运行失败: {e}")
        return
    
    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件不存在: {args.input_file}")
        return
    
    print("COSMIC校验功能")
    print("=" * 50)
    print(f"输入文件: {args.input_file}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    try:
        # 创建校验器
        validator = CosmicValidator()
        
        # 执行校验
        result = validator.validate_file(args.input_file, args.output)
        
        if "error" in result:
            print(f"校验失败: {result['error']}")
            return
        
        # 显示结果摘要
        print("\n" + "=" * 50)
        print("校验完成!")
        print("=" * 50)
        
        summary = result.get('summary', {})
        print(f"总模块数: {summary.get('total_modules', 0)}")
        print(f"原始记录数: {summary.get('total_original_records', 0)}")
        print(f"发现问题数: {summary.get('total_issues_found', 0)}")
        print(f"修复记录数: {summary.get('records_with_fixes', 0)}")
        print(f"整体合规率: {summary.get('overall_compliance_rate', 'N/A')}")
        
        # 显示问题类型统计
        all_issues = result.get('all_issues', [])
        if all_issues:
            print(f"\n发现的问题类型:")
            issue_types = {}
            for issue in all_issues:
                issue_type = issue.get('issue_type', '未知')
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
            
            for issue_type, count in issue_types.items():
                print(f"  {issue_type}: {count} 个")
        
        print(f"\n结果文件已保存到 debug/ 目录")
        print(f"详细报告请查看: debug/cosmic_validation_report.txt")
        
    except Exception as e:
        print(f"校验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
